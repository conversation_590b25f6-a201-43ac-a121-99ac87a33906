import { AuthOptions, getServerSession } from "next-auth";
import { JWT } from "next-auth/jwt";
import AzureADProvider from "next-auth/providers/azure-ad";

async function refreshAccessToken(token: JWT) {
  try {
    const url = `https://login.microsoftonline.com/${process.env.AZURE_AD_TENANT_ID}/oauth2/v2.0/token`;

    const body = new URLSearchParams({
      client_id: process.env.AZURE_AD_CLIENT_ID || "azure-ad-client-id",
      client_secret:
        process.env.AZURE_AD_CLIENT_SECRET || "azure-ad-client-secret",
      scope: "openid profile offline_access",
      grant_type: "refresh_token",
      refresh_token: token.refreshToken as string,
    });

    const response = await fetch(url, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      method: "POST",
      body,
    });

    if (!response.ok) {
      throw await response.json();
    }

    const refreshedTokens = await response.json();

    return {
      ...token,
      accessToken: refreshedTokens.access_token,
      accessTokenExpires: Date.now() + refreshedTokens.expires_in * 1000,
      refreshToken: refreshedTokens.refresh_token ?? token.refreshToken,
    };
  } catch (error) {
    console.error(error);
    return {
      ...token,
      error: "RefreshAccessTokenError",
    };
  }
}

const authOptions: AuthOptions = {
  providers: [
    AzureADProvider({
      clientId: process.env.AZURE_AD_CLIENT_ID || "azure-ad-client-id",
      clientSecret:
        process.env.AZURE_AD_CLIENT_SECRET || "azure-ad-client-secret",
      tenantId: process.env.AZURE_AD_TENANT_ID || "azure-ad-tenant-id",
      authorization: {
        params: { scope: "email openid profile offline_access" },
      },
      checks: ["none"],
      profile(profile, tokens) {
        return {
          id: profile.oid,
          name: profile.name,
          username: profile.preferred_username,
          email: profile.email,
          roles: profile.roles,
        };
      },
    }),
  ],
  session: {
    strategy: "jwt",
  },
  secret: process.env.JWT_SECRET || "jwt-secret",
  callbacks: {
    async jwt({ token, user, account, profile }) {
      if (account && user) {
        return {
          accessToken: account.id_token,
          accessTokenExpires: account?.expires_at
            ? account.expires_at * 1000
            : 0,
          refreshToken: account.refresh_token,
          user,
          username: user.username,
          roles: user.roles,
        };
      }

      if (
        Date.now() <
          (token as JWT & { accessTokenExpires: number }).accessTokenExpires ||
        0
      ) {
        return token;
      }

      return refreshAccessToken(token);
    },
    async session({ session, token }: any) {
      if (session) {
        session.user = token.user;
        session.error = token.error;
        session.user.access_token_expires_at = token.accessTokenExpires;
        session.expires = token.accessTokenExpires;
        session.user.username = token.username;
        session.user.roles = token.roles;
        session.isGS = token.roles.includes("Tim_GS_User");
        session.isGsEditor = token.roles.includes("Tim_GS_Docs_Mng");
      }

      console.log(session);

      return session;
    },
  },
};

/**
 * Helper function to get the session on the server without having to import the authOptions object every single time
 * @returns The session object or null
 */
const getSession = () => getServerSession(authOptions);

export { authOptions, getSession };
