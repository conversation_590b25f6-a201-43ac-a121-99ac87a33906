"use client";

import classes from "./SignIn.module.css";
import { signIn, useSession } from "next-auth/react";
import { useEffect } from "react";
import { useRouter } from "next/navigation";

const SignInPage = () => {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    console.log(process.env.AZURE_CALLBACK_URL);
    if (status === "unauthenticated") {
      signIn("azure-ad", {
        callbackUrl: process.env.AZURE_CALLBACK_URL,
      });
    }
    if (status === "authenticated" && session?.isGS) {
      router.push("/");
    }
  }, [status, session, router]);

  return (
    <div className={classes.login_container}>
      {status === "loading" ? (
        <h1>Loading.....</h1>
      ) : status === "authenticated" && session && !session.isGS ? (
        <div className="flex flex-col items-center justify-center min-h-[60vh] text-center px-4">
          <h2 className="text-xl font-semibold text-gray-800 mb-2">
            Not authorized
          </h2>
          <p className="text-gray-600 mb-6">
            You don't have permission to access this application.
          </p>
          <a
            href="https://timapps.emea.bosch.com/"
            className="px-4 py-2 rounded-lg bg-[#007bc0] text-white hover:bg-[#005a9c] transition-colors"
          >
            Go to Home Page
          </a>
        </div>
      ) : (
        <h1>Please wait while you are being redirected...</h1>
      )}
    </div>
  );
};

export default SignInPage;
